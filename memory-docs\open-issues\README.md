# Open Issues

This directory tracks high-priority issues that are still broken and need work.

## Format
Each issue should be documented in a separate file named: `YYYY-MM-DD-issue-description.md`

## Current Open Issues

*No critical open issues at this time.*

## Issue Template

When creating a new issue file, use this template:

```markdown
# Issue Title

**Date Reported**: YYYY-MM-DD
**Priority**: High/Medium/Low
**Component**: Frontend/Backend/Database/Infrastructure
**Reporter**: Name/Team

## Description
Brief description of the issue and its impact.

## Steps to Reproduce
1. Step one
2. Step two
3. Step three

## Expected Behavior
What should happen instead.

## Current Workaround
Any temporary solutions in place.

## Investigation Notes
- Finding 1
- Finding 2
- Finding 3

## Next Steps
- [ ] Action item 1
- [ ] Action item 2
- [ ] Action item 3
```

## Workflow
1. When a critical issue is identified, create a new file in this directory
2. Update the issue file as investigation progresses
3. When the issue is resolved, move the documentation to `fix-guides/` directory
4. Delete the file from this directory once the fix is confirmed stable
