# Feature: Analysis Companion Chat with MCP-use-ts Integration

## Persona & Stack
- **Persona**: Senior Full-Stack Developer with AI/LLM Integration Expertise
- **Frameworks**: React 19, Vite, Hono API, Tailwind CSS, ShadCN UI
- **Languages**: TypeScript, Node.js
- **DB/API**: Supabase PostgreSQL, Firebase Auth
- **AI/MCP**: mcp-use-ts library, OpenAI GPT models, LangChain.js
- **Package Manager**: pnpm

## Goal
Create an intelligent analysis companion chat interface that leverages the mcp-use-ts library to provide users with AI-powered analysis capabilities. The chat should connect to various MCP servers for enhanced functionality like web browsing, file operations, and data analysis, while maintaining conversation memory and providing real-time streaming responses.

## Scope & Constraints
- **Scope**: Create new chat interface in `ui/src/components/chat/` and API endpoints in `server/src/routes/`
- **Integration**: Integrate with existing Firebase Auth and Supabase database
- **Limitations**: No modifications to core authentication or database schemas
- **Environment**: Use existing development environment with embedded PostgreSQL and Firebase emulators

## Context
- **Code Injection**: Reference existing UI components from `ui/src/components/ui/` for consistent styling
- **Memory Recall**: Recall and reference any relevant memories from the memory folder to ensure consistency with existing architecture and patterns used in the volo-app template
- **Library Reference**: [mcp-use-ts](https://github.com/mcp-use/mcp-use-ts) - Unified MCP Client Library for connecting LLMs to MCP servers

## Plan
1. **Research and Setup**
   - Install mcp-use-ts and required dependencies (langchain, @langchain/openai, dotenv)
   - Create environment configuration for OpenAI API key
   - Set up MCP server configuration for analysis tools

2. **Backend Implementation**
   - Create `/api/chat` endpoint in Hono server for streaming chat responses
   - Implement MCPAgent with conversation memory and streaming capabilities
   - Configure MCP servers for analysis (filesystem, web browsing, data processing)
   - Add proper error handling and session management

3. **Frontend Components**
   - Create chat interface components using ShadCN UI patterns
   - Implement real-time streaming with AI SDK integration
   - Add conversation history and memory management
   - Create analysis tool indicators and status displays

4. **Integration & Testing**
   - Connect chat interface to backend streaming API
   - Test with various MCP servers and analysis scenarios
   - Implement proper authentication integration
   - Add responsive design and accessibility features

## Requirements

### Backend Requirements
- **MCPAgent Configuration**: Set up with OpenAI GPT-4 model, conversation memory enabled, max 15 steps
- **MCP Servers**: Configure multiple servers for analysis:
  - `@modelcontextprotocol/server-everything` for general tools
  - `@playwright/mcp` for web browsing and automation
  - File system tools for document analysis
- **Streaming API**: Implement `/api/chat` endpoint with AI SDK compatibility
- **Session Management**: Proper cleanup of MCP sessions and memory management
- **Authentication**: Integrate with existing Firebase Auth middleware

### Frontend Requirements
- **Chat Interface**: Clean, modern chat UI with message bubbles and typing indicators
- **Streaming Support**: Real-time token-by-token streaming with `useCompletion` hook
- **Tool Visibility**: Display when AI is using specific tools (🔧 Using tool: X)
- **Conversation Memory**: Clear history button and persistent conversation state
- **Analysis Features**: 
  - File upload for document analysis
  - URL input for web page analysis
  - Data visualization capabilities
- **Responsive Design**: Mobile-friendly interface with proper touch interactions

### Technical Requirements
- **Error Handling**: Graceful error handling with user-friendly messages
- **Performance**: Efficient streaming with proper debouncing and cleanup
- **Security**: Validate all inputs and sanitize MCP tool outputs
- **Accessibility**: ARIA labels, keyboard navigation, screen reader support

## External Libraries
- [mcp-use-ts](https://github.com/mcp-use/mcp-use-ts) - Unified MCP Client Library for TypeScript
- [LangChain.js](https://js.langchain.com/) - Framework for developing applications with LLMs
- [Vercel AI SDK](https://sdk.vercel.ai/) - Toolkit for building AI-powered streaming UIs
- [@langchain/openai](https://www.npmjs.com/package/@langchain/openai) - OpenAI integration for LangChain
- [@modelcontextprotocol/server-everything](https://www.npmjs.com/package/@modelcontextprotocol/server-everything) - Comprehensive MCP server
- [@playwright/mcp](https://www.npmjs.com/package/@playwright/mcp) - Browser automation MCP server

## Verification & Output

### Expected Files
- `server/src/routes/chat.ts` - Chat API endpoint with streaming support
- `server/src/lib/mcp-agent.ts` - MCPAgent configuration and utilities
- `ui/src/components/chat/ChatInterface.tsx` - Main chat component
- `ui/src/components/chat/MessageBubble.tsx` - Individual message component
- `ui/src/components/chat/ToolIndicator.tsx` - Tool usage indicator
- `ui/src/hooks/useChat.ts` - Custom hook for chat functionality
- `ui/src/pages/AnalysisChat.tsx` - Chat page component

### Verification Plan
1. **Unit Tests**: Test MCPAgent configuration and chat API endpoints
2. **Integration Tests**: Verify streaming functionality and MCP server connections
3. **Manual Testing**: 
   - Test conversation memory and history clearing
   - Verify tool usage indicators and streaming responses
   - Test file upload and URL analysis features
   - Validate authentication integration
4. **Performance Testing**: Ensure efficient streaming and proper cleanup
5. **Accessibility Testing**: Verify keyboard navigation and screen reader compatibility

### Success Criteria
- Chat interface loads and connects to backend successfully
- Real-time streaming works with token-by-token updates
- MCP tools are properly integrated and display usage indicators
- Conversation memory persists across messages
- File and URL analysis features work correctly
- Authentication is properly integrated
- Interface is responsive and accessible
