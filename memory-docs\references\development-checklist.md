# Development Checklist

## Pre-Development Setup

### Environment Setup
- [ ] Node.js 20+ installed
- [ ] pnpm 8+ installed
- [ ] Git configured with user name and email
- [ ] IDE/Editor configured with TypeScript support
- [ ] Firebase CLI installed (optional, for advanced auth setup)

### Project Setup
- [ ] Clone repository
- [ ] Run `pnpm install` in root directory
- [ ] Verify all workspaces install correctly
- [ ] Run `pnpm run dev` to start development environment
- [ ] Verify all services start without errors

### Development Environment Verification
- [ ] Frontend accessible at assigned port (usually 5173)
- [ ] Backend API responding at assigned port (usually 8787)
- [ ] Database connection successful (check `/api/v1/db-test`)
- [ ] Firebase Auth emulator running
- [ ] Port management working (no conflicts)

## Feature Development Workflow

### Before Starting a Feature
- [ ] Create feature branch from main/develop
- [ ] Update implementation plan with feature details
- [ ] Review existing architecture documentation
- [ ] Identify required database schema changes
- [ ] Plan API endpoints and data flow

### Database Changes
- [ ] Create/update schema files in `server/src/schema/`
- [ ] Export new schemas from main schema file
- [ ] Run `cd server && pnpm db:push` to apply changes
- [ ] Verify schema changes in database
- [ ] Update TypeScript types if needed

### Backend Development
- [ ] Add new routes to appropriate router (public/protected)
- [ ] Implement middleware if needed
- [ ] Add input validation using TypeScript types
- [ ] Implement business logic
- [ ] Add error handling
- [ ] Test endpoints manually or with tools

### Frontend Development
- [ ] Create/update components in `ui/src/components/`
- [ ] Add new pages in `ui/src/pages/` if needed
- [ ] Update routing configuration
- [ ] Implement API integration in `lib/serverComm.ts`
- [ ] Add proper error handling and loading states
- [ ] Ensure responsive design with Tailwind CSS

### UI Components
- [ ] Use existing ShadCN components when possible
- [ ] Install new components: `cd ui && npx shadcn add [component]`
- [ ] Follow component-first architecture
- [ ] Ensure accessibility (ARIA labels, keyboard navigation)
- [ ] Test component in different screen sizes

## Code Quality Checklist

### TypeScript
- [ ] No TypeScript errors in build
- [ ] Proper type definitions for all functions
- [ ] No `any` types (use proper typing)
- [ ] Interface/type definitions for API responses
- [ ] Proper error type handling

### Code Organization
- [ ] Files under 500 lines (split if larger)
- [ ] One responsibility per file
- [ ] Proper import organization (relative paths for internal)
- [ ] Consistent naming conventions
- [ ] Co-located related files

### Performance
- [ ] No unnecessary re-renders in React components
- [ ] Proper use of React hooks (useCallback, useMemo)
- [ ] Optimized database queries
- [ ] Minimal bundle size impact
- [ ] Lazy loading for large components

### Security
- [ ] Input validation on all API endpoints
- [ ] Proper authentication checks on protected routes
- [ ] No sensitive data in client-side code
- [ ] Environment variables properly configured
- [ ] SQL injection prevention (parameterized queries)

## Testing Checklist

### Manual Testing
- [ ] Feature works in development environment
- [ ] Authentication flow works correctly
- [ ] Error states handled gracefully
- [ ] Loading states provide good UX
- [ ] Responsive design on mobile/tablet/desktop
- [ ] Cross-browser compatibility (Chrome, Firefox, Safari)

### Automated Testing (Future)
- [ ] Unit tests for utility functions
- [ ] Component tests for React components
- [ ] API endpoint tests
- [ ] Integration tests for critical flows
- [ ] E2E tests for user journeys

## Pre-Commit Checklist

### Code Review
- [ ] Code follows project conventions
- [ ] No console.log statements in production code
- [ ] Proper error handling implemented
- [ ] Documentation updated if needed
- [ ] No hardcoded values (use environment variables)

### Documentation Updates
- [ ] Update `memory-docs/features.md` if shipping new feature
- [ ] Update API documentation if endpoints changed
- [ ] Update implementation plan if architecture changed
- [ ] Add entry to `memory-docs/progress.md`

### Build Verification
- [ ] Frontend builds without errors: `cd ui && pnpm build`
- [ ] Backend compiles without errors: `cd server && pnpm build`
- [ ] No TypeScript errors in either workspace
- [ ] All dependencies properly installed

## Deployment Checklist

### Pre-Deployment
- [ ] All tests passing
- [ ] Code reviewed and approved
- [ ] Environment variables configured for production
- [ ] Database migrations applied (if any)
- [ ] Feature flags configured (if applicable)

### Production Database Setup (First Time)
- [ ] External PostgreSQL provider configured (Neon/Supabase)
- [ ] Database connection string added to environment
- [ ] Schema applied to production database
- [ ] Database connectivity verified

### Cloudflare Deployment
- [ ] Backend deployed: `cd server && pnpm run deploy`
- [ ] Frontend connected to Cloudflare Pages
- [ ] Environment variables set in Cloudflare dashboard
- [ ] Custom domain configured (if applicable)
- [ ] SSL certificate active

### Post-Deployment
- [ ] Production endpoints responding correctly
- [ ] Authentication working with production Firebase
- [ ] Database operations functioning
- [ ] Error monitoring active
- [ ] Performance metrics baseline established

## Troubleshooting Common Issues

### Development Issues
- [ ] Port conflicts: Check port manager output
- [ ] Database connection: Verify embedded PostgreSQL started
- [ ] Auth issues: Check Firebase emulator status
- [ ] Build errors: Clear node_modules and reinstall
- [ ] TypeScript errors: Check for missing type definitions

### Production Issues
- [ ] API not responding: Check Cloudflare Worker logs
- [ ] Database errors: Verify connection string and permissions
- [ ] Auth failures: Check Firebase project configuration
- [ ] CORS errors: Verify allowed origins in API configuration
- [ ] Performance issues: Check Cloudflare analytics
