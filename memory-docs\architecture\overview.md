# Architecture Overview

## System Diagram

```mermaid
graph TD
  A[React UI (Vite)] -- HTTP + Auth Bearer --> B[Hono API]
  B -- Drizzle ORM --> C[(PostgreSQL)]
  A -- Firebase SDK --> D[Firebase Auth]
  B -- Firebase Admin --> D
```

## Runtime Modes
- Local: Embedded Postgres, Firebase Auth emulator, Node server
- Production: Cloudflare Workers (API) + Pages (UI), remote Postgres (Neon/Supabase), Firebase Auth prod

## Key Flows
1) Authentication
   - UI signs in via Firebase → obtains ID token
   - API `authMiddleware` verifies token, upserts/loads `app.users`, sets `c.set('user')`

2) Data Access
   - `lib/db.ts` chooses Neon vs Postgres client by `DATABASE_URL`
   - Drizzle schema in `server/src/schema/users.ts`

3) Routing
   - Public: `/api/v1/hello`, `/api/v1/db-test`
   - Protected (with middleware): `/api/v1/protected/*`

## Notable Files
- UI: `ui/src/App.tsx`, `ui/src/lib/serverComm.ts`, `ui/src/lib/auth-context.tsx`
- API: `server/src/api.ts`, `server/src/middleware/auth.ts`, `server/src/lib/db.ts`, `server/src/lib/env.ts`


