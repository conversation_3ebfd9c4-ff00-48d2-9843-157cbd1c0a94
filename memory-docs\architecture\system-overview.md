# System Architecture Overview

## High-Level Architecture

```mermaid
graph TD
  A[React UI - Vite] -- HTTP + Auth Bearer --> B[Hono API - Node.js]
  B -- Drizzle ORM --> C[(PostgreSQL)]
  A -- Firebase SDK --> D[Firebase Auth]
  B -- Firebase Admin --> D
  E[Cloudflare Pages] --> A
  F[Cloudflare Workers] --> B
  G[Neon/Supabase/Custom] --> C
  
  subgraph "Local Development"
    H[Embedded PostgreSQL]
    I[Firebase Auth Emulator]
    J[Node.js Server]
  end
  
  subgraph "Production"
    E
    F
    G
    D
  end
```

## Runtime Environments

### Local Development Mode
- **Frontend**: Vite dev server (default port 5173)
- **Backend**: Node.js server (default port 8787)
- **Database**: Embedded PostgreSQL (dynamic port allocation starting from 5433)
- **Authentication**: Firebase Auth emulator
- **Benefits**: Zero external dependencies, instant setup, offline development

### Production Mode
- **Frontend**: Cloudflare Pages (static hosting with global CDN)
- **Backend**: Cloudflare Workers (serverless edge computing)
- **Database**: External PostgreSQL (Neon, Supabase, or custom)
- **Authentication**: Production Firebase Auth
- **Benefits**: Global scale, automatic scaling, edge performance

## Core Components

### Frontend Application (React + TypeScript)
- **Framework**: React 18 with TypeScript for type safety
- **Build Tool**: Vite for fast development and optimized builds
- **Styling**: Tailwind CSS with ShadCN component library
- **State Management**: React Context for authentication, local state for components
- **Routing**: File-based routing with protected route guards

### Backend API (Hono + Node.js)
- **Framework**: Hono for lightweight, fast API development
- **Runtime**: Node.js (development) / Cloudflare Workers (production)
- **Middleware**: Authentication, CORS, logging, error handling
- **Architecture**: RESTful API with clear public/protected endpoint separation

### Database Layer (PostgreSQL + Drizzle)
- **Database**: PostgreSQL for ACID compliance and advanced features
- **ORM**: Drizzle for type-safe database operations
- **Schema**: Application-specific schema (`app`) for data isolation
- **Migrations**: SQL-based migrations for version control

### Authentication System (Firebase Auth)
- **Provider**: Firebase Auth for robust authentication
- **Local Development**: Firebase Auth emulator for offline development
- **Token Flow**: JWT tokens for stateless authentication
- **User Management**: Automatic user synchronization with local database

## Data Flow Patterns

### Authentication Flow
1. User initiates sign-in through React UI
2. Firebase Auth handles authentication (Google, email/password)
3. Frontend receives Firebase ID token
4. API requests include token in Authorization header
5. Backend middleware verifies token with Firebase Admin SDK
6. User information is upserted to local database
7. User context is available in protected routes

### API Request Flow
1. Frontend makes HTTP request with authentication token
2. Hono middleware processes request (CORS, auth, logging)
3. Route handler executes business logic
4. Database operations performed through Drizzle ORM
5. Response returned with appropriate status and data
6. Frontend updates UI based on response

### Database Operations
1. Schema definitions in TypeScript provide type safety
2. Drizzle ORM generates SQL queries from TypeScript operations
3. Connection pooling handles database connections efficiently
4. Transactions ensure data consistency for complex operations

## Security Architecture

### Authentication Security
- Firebase ID tokens with configurable expiration
- Token verification on every protected API request
- Automatic token refresh handling in frontend
- Secure token storage in HTTP-only cookies (production)

### API Security
- CORS configuration for cross-origin request control
- Input validation using TypeScript types and runtime checks
- Rate limiting (planned for production)
- SQL injection prevention through parameterized queries

### Environment Security
- Environment variable isolation between local/production
- Secrets management through Cloudflare Workers environment
- Database connection encryption in production
- Private schema isolation for application data

## Performance Considerations

### Frontend Performance
- Vite for fast development builds and hot module replacement
- Code splitting for optimized bundle sizes
- Lazy loading for route-based components
- Tailwind CSS purging for minimal CSS bundles

### Backend Performance
- Hono's lightweight framework for minimal overhead
- Connection pooling for database efficiency
- Cloudflare Workers for edge computing (production)
- Caching strategies for frequently accessed data

### Database Performance
- Proper indexing on frequently queried columns
- Query optimization through Drizzle's type-safe queries
- Connection pooling to manage database connections
- Read replicas for scaling (future consideration)

## Scalability Design

### Horizontal Scaling
- Stateless API design for easy horizontal scaling
- Cloudflare Workers automatic scaling
- Database connection pooling for concurrent requests
- CDN distribution for static assets

### Vertical Scaling
- Efficient database queries to reduce resource usage
- Optimized bundle sizes for faster loading
- Memory-efficient state management
- Lazy loading for reduced initial load times
