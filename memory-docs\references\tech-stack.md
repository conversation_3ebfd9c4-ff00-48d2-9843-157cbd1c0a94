# Tech Stack Reference

## Frontend Stack

### Core Framework
- **React 18**: Modern React with concurrent features and automatic batching
- **TypeScript**: Type safety and enhanced developer experience
- **Vite**: Fast build tool with hot module replacement and optimized builds

### UI & Styling
- **Tailwind CSS**: Utility-first CSS framework for rapid styling
- **ShadCN/UI**: High-quality, accessible React components built on Radix UI
- **Lucide React**: Beautiful, customizable SVG icons

### State Management
- **React Context**: Built-in state management for authentication and global state
- **React Hooks**: useState, useEffect, useContext for local component state
- **Custom Hooks**: Reusable stateful logic (e.g., useAuth, useMobile)

### Development Tools
- **ESLint**: Code linting with TypeScript and React rules
- **Prettier**: Code formatting (configured via ESLint)
- **TypeScript Config**: Strict type checking with modern ES features

## Backend Stack

### Core Framework
- **Hono**: Lightweight, fast web framework for TypeScript
- **Node.js**: JavaScript runtime for development environment
- **TypeScript**: Type-safe server-side development

### Database & ORM
- **PostgreSQL**: Robust, ACID-compliant relational database
- **Drizzle ORM**: Type-safe, SQL-like ORM with excellent TypeScript integration
- **Embedded PostgreSQL**: Local development database (embedded-postgres package)

### Authentication
- **Firebase Auth**: Robust authentication service with multiple providers
- **Firebase Admin SDK**: Server-side token verification and user management
- **JWT Tokens**: Stateless authentication with Firebase ID tokens

### Middleware & Utilities
- **CORS**: Cross-origin resource sharing configuration
- **Logger**: Request/response logging for debugging
- **Environment Management**: Runtime-agnostic environment variable handling

## Database Stack

### Primary Database
- **PostgreSQL 15+**: Production-grade relational database
- **Schema Namespacing**: Application tables in `app` schema for isolation
- **Connection Pooling**: Efficient connection management

### ORM & Migrations
- **Drizzle ORM**: Type-safe database operations with SQL-like syntax
- **Drizzle Kit**: Schema management and migration tools
- **SQL Migrations**: Version-controlled database schema changes

### Development Database
- **Embedded PostgreSQL**: Zero-configuration local database
- **Dynamic Port Allocation**: Automatic port assignment to prevent conflicts
- **Data Persistence**: Local data storage in `data/postgres/` directory

## Deployment Stack

### Production Hosting
- **Cloudflare Workers**: Serverless edge computing for API backend
- **Cloudflare Pages**: Static site hosting with global CDN for frontend
- **Cloudflare DNS**: Domain management and DNS configuration

### Database Hosting Options
- **Neon**: Serverless PostgreSQL with branching and autoscaling
- **Supabase**: Open-source Firebase alternative with PostgreSQL
- **Custom PostgreSQL**: Self-hosted or managed PostgreSQL instances

### Build & Deployment
- **Wrangler**: Cloudflare Workers CLI for deployment and management
- **Git Integration**: Automatic deployments via Git repository connections
- **Environment Variables**: Secure configuration management in Cloudflare dashboard

## Development Tools

### Package Management
- **pnpm**: Fast, disk space efficient package manager
- **Workspaces**: Monorepo management for frontend and backend
- **Lock Files**: Deterministic dependency resolution

### Development Environment
- **Concurrently**: Run multiple development servers simultaneously
- **Get-Port**: Dynamic port allocation for conflict resolution
- **Firebase Tools**: Local Firebase emulator for authentication

### Code Quality
- **TypeScript**: Static type checking across the entire stack
- **ESLint**: Consistent code style and error detection
- **Git Hooks**: Pre-commit validation (future implementation)

## External Services

### Authentication
- **Firebase Authentication**: Google, email/password, and other providers
- **Firebase Console**: User management and authentication configuration
- **Firebase Emulator**: Local authentication testing

### Monitoring & Analytics (Future)
- **Cloudflare Analytics**: Performance and usage metrics
- **Sentry**: Error tracking and performance monitoring
- **LogRocket**: User session recording and debugging

## Version Requirements

### Node.js Ecosystem
- **Node.js**: >=20.0.0 (LTS recommended)
- **pnpm**: >=8.0.0
- **TypeScript**: ^5.0.0
- **React**: ^18.0.0

### Database
- **PostgreSQL**: >=13.0 (embedded version: 17.5.0)
- **Drizzle ORM**: Latest stable version
- **Drizzle Kit**: Latest stable version

### Build Tools
- **Vite**: ^5.0.0
- **Tailwind CSS**: ^3.0.0
- **ESLint**: ^8.0.0

## Configuration Files

### Frontend Configuration
- `ui/package.json`: Frontend dependencies and scripts
- `ui/vite.config.ts`: Vite build configuration
- `ui/tailwind.config.js`: Tailwind CSS customization
- `ui/components.json`: ShadCN component configuration
- `ui/tsconfig.json`: TypeScript configuration for frontend

### Backend Configuration
- `server/package.json`: Backend dependencies and scripts
- `server/tsconfig.json`: TypeScript configuration for backend
- `server/drizzle.config.ts`: Database ORM configuration
- `server/wrangler.toml`: Cloudflare Workers deployment configuration

### Root Configuration
- `package.json`: Workspace configuration and global scripts
- `pnpm-workspace.yaml`: pnpm workspace definition
- `firebase.json`: Firebase emulator configuration

## Environment Variables

### Development
- `DATABASE_URL`: Local PostgreSQL connection string
- `FIREBASE_PROJECT_ID`: Firebase project identifier
- `PORT`: Backend server port (dynamically assigned)

### Production
- `DATABASE_URL`: Production PostgreSQL connection string
- `FIREBASE_PROJECT_ID`: Production Firebase project ID
- `VITE_API_URL`: API base URL for frontend (optional)

## Performance Characteristics

### Frontend Performance
- **Bundle Size**: Optimized with Vite tree-shaking and code splitting
- **Load Time**: Fast initial load with lazy loading and CDN distribution
- **Runtime Performance**: React 18 concurrent features for smooth UX

### Backend Performance
- **Cold Start**: Minimal with Hono's lightweight framework
- **Response Time**: Sub-100ms for simple operations
- **Throughput**: High concurrency with Cloudflare Workers edge computing

### Database Performance
- **Query Performance**: Optimized with proper indexing and Drizzle ORM
- **Connection Management**: Efficient pooling and connection reuse
- **Scalability**: Horizontal scaling with read replicas (future)
