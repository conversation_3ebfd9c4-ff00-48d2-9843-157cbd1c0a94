# Fix Guides & Post-Mortems

This directory contains post-mortem analyses and resolution guides for critical bugs and incidents.

## Purpose
- Document root causes of critical issues
- Share knowledge about fixes and preventative measures
- Build institutional knowledge for future problem-solving
- Track patterns in system failures

## File Naming Convention
`YYYY-MM-DD-incident-summary.md`

Example: `2025-08-11-database-connection-timeout.md`

## Post-Mortem Template

```markdown
# Incident Title - YYYY-MM-DD

**Incident Date**: YYYY-MM-DD HH:MM UTC
**Resolution Date**: YYYY-MM-DD HH:MM UTC
**Severity**: Critical/High/Medium/Low
**Duration**: X hours/minutes
**Affected Components**: List of systems impacted

## Summary
Brief description of what happened and the impact.

## Timeline
- **HH:MM** - Initial incident detection
- **HH:MM** - Investigation started
- **HH:MM** - Root cause identified
- **HH:MM** - Fix implemented
- **HH:MM** - Resolution confirmed

## Root Cause Analysis
### Primary Cause
Detailed explanation of the main cause.

### Contributing Factors
- Factor 1: Description
- Factor 2: Description
- Factor 3: Description

## Impact Assessment
- **Users Affected**: Number/percentage of users
- **Services Affected**: List of impacted services
- **Data Impact**: Any data loss or corruption
- **Business Impact**: Revenue, reputation, or operational impact

## Resolution
### Immediate Fix
Steps taken to resolve the immediate issue.

### Permanent Fix
Long-term solution implemented to prevent recurrence.

## Preventative Measures
- [ ] Monitoring improvement 1
- [ ] Process change 1
- [ ] Technical improvement 1
- [ ] Documentation update 1

## Lessons Learned
- Key insight 1
- Key insight 2
- Key insight 3

## Action Items
- [ ] Action item 1 (Owner: Name, Due: Date)
- [ ] Action item 2 (Owner: Name, Due: Date)
- [ ] Action item 3 (Owner: Name, Due: Date)

## Related Issues
- Link to issue tracker
- Related incidents
- Similar problems in the past
```

## Current Fix Guides

*No critical incidents have occurred yet. This section will be updated as issues are resolved.*

## Best Practices

### When to Create a Post-Mortem
- Any production outage or degradation
- Security incidents or breaches
- Data loss or corruption events
- Critical bugs affecting user experience
- Performance issues causing significant impact

### Post-Mortem Process
1. **Immediate Response**: Focus on resolution first
2. **Data Collection**: Gather logs, metrics, and timeline
3. **Root Cause Analysis**: Identify primary and contributing causes
4. **Documentation**: Create comprehensive post-mortem
5. **Review**: Team review of findings and action items
6. **Follow-up**: Track completion of preventative measures

### Blameless Culture
- Focus on systems and processes, not individuals
- Encourage honest reporting and discussion
- Learn from failures to improve resilience
- Share knowledge across the team
