# API Schema Reference

## Base Configuration
- **Local Base URL**: `http://localhost:8787`
- **Production Base URL**: Cloudflare Worker URL (configured via `VITE_API_URL`)
- **API Version**: v1
- **Content Type**: `application/json`
- **Authentication**: Bearer token in Authorization header

## Response Format
All API responses follow a consistent format:
```json
{
  "data": {},
  "timestamp": "2025-08-11T00:00:00.000Z",
  "status": "success"
}
```

Error responses:
```json
{
  "error": "Error message",
  "details": "Additional error details (optional)",
  "timestamp": "2025-08-11T00:00:00.000Z",
  "status": "error"
}
```

## Public Endpoints

### GET /api/v1/hello
**Description**: Health check endpoint
**Authentication**: None required
**Response**:
```json
{
  "message": "Hello from Hono!"
}
```

### GET /api/v1/db-test
**Description**: Database connectivity test
**Authentication**: None required
**Response**:
```json
{
  "message": "Database connection successful!",
  "users": [
    {
      "id": "user_id",
      "email": "<EMAIL>"
    }
  ],
  "connectionHealthy": true,
  "usingLocalDatabase": true,
  "timestamp": "2025-08-11T00:00:00.000Z"
}
```

## Protected Endpoints

### GET /api/v1/protected/me
**Description**: Get current authenticated user information
**Authentication**: Required (Bearer token)
**Response**:
```json
{
  "user": {
    "id": "firebase_user_id",
    "email": "<EMAIL>",
    "display_name": "User Name",
    "photo_url": "https://example.com/photo.jpg",
    "created_at": "2025-08-11T00:00:00.000Z",
    "updated_at": "2025-08-11T00:00:00.000Z"
  }
}
```

## Database Schema

### Users Table (`app.users`)
```sql
CREATE SCHEMA IF NOT EXISTS app;

CREATE TABLE app.users (
  id TEXT PRIMARY KEY,                    -- Firebase UID
  email TEXT UNIQUE NOT NULL,             -- User email
  display_name TEXT,                      -- Display name
  photo_url TEXT,                         -- Profile photo URL
  created_at TIMESTAMP DEFAULT NOW(),     -- Account creation
  updated_at TIMESTAMP DEFAULT NOW()      -- Last update
);
```

### TypeScript Types
```typescript
export type User = {
  id: string;
  email: string;
  display_name: string | null;
  photo_url: string | null;
  created_at: Date;
  updated_at: Date;
};

export type NewUser = {
  id: string;
  email: string;
  display_name?: string;
  photo_url?: string;
};
```

## Authentication Flow

### Token Acquisition (Frontend)
```typescript
import { getAuth, signInWithPopup, GoogleAuthProvider } from 'firebase/auth';

const auth = getAuth();
const provider = new GoogleAuthProvider();

const result = await signInWithPopup(auth, provider);
const token = await result.user.getIdToken();
```

### API Request with Authentication
```typescript
const response = await fetch('/api/v1/protected/me', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});
```

### Token Verification (Backend)
```typescript
import { authMiddleware } from './middleware/auth';

// Protected routes automatically have user context
protectedRoutes.get('/me', (c) => {
  const user = c.get('user'); // Authenticated user object
  return c.json({ user });
});
```

## Error Codes

### HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation error)
- `401` - Unauthorized (missing or invalid token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `500` - Internal Server Error

### Custom Error Types
```typescript
type APIError = {
  error: string;
  details?: string;
  code?: string;
  timestamp: string;
};
```

## Rate Limiting (Future)
- **Public Endpoints**: 100 requests per minute per IP
- **Protected Endpoints**: 1000 requests per minute per user
- **Headers**: `X-RateLimit-Limit`, `X-RateLimit-Remaining`, `X-RateLimit-Reset`

## Versioning Strategy
- **Current Version**: v1
- **URL Pattern**: `/api/v{version}/{endpoint}`
- **Backward Compatibility**: Maintained for at least one major version
- **Deprecation Notice**: 6 months advance notice for breaking changes
